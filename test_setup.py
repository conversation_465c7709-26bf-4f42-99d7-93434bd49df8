#!/usr/bin/env python3
"""
Test script to validate the setup and basic functionality.
"""

import os
import sys
import tempfile
from io import BytesIO

def test_imports():
    """Test that all required modules can be imported."""
    print("🔍 Testing imports...")

    try:
        import streamlit
        print("   ✅ Streamlit")
    except ImportError as e:
        print(f"   ❌ Streamlit: {e}")
        return False

    try:
        from pptx import Presentation
        print("   ✅ python-pptx")
    except ImportError as e:
        print(f"   ❌ python-pptx: {e}")
        return False

    try:
        import fitz  # PyMuPDF
        print("   ✅ PyMuPDF")
    except ImportError as e:
        print(f"   ❌ PyMuPDF: {e}")
        return False

    try:
        from docx import Document
        print("   ✅ python-docx")
    except ImportError as e:
        print(f"   ❌ python-docx: {e}")
        return False

    try:
        import requests
        print("   ✅ requests")
    except ImportError as e:
        print(f"   ❌ requests: {e}")
        return False

    try:
        from dotenv import load_dotenv
        print("   ✅ python-dotenv")
    except ImportError as e:
        print(f"   ❌ python-dotenv: {e}")
        return False

    try:
        from PIL import Image
        print("   ✅ Pillow")
    except ImportError as e:
        print(f"   ❌ Pillow: {e}")
        return False

    return True


def test_local_modules():
    """Test that local modules can be imported."""
    print("\n🔍 Testing local modules...")

    try:
        import config
        print("   ✅ config")
    except ImportError as e:
        print(f"   ❌ config: {e}")
        return False

    try:
        from content_extractor import ContentExtractor
        print("   ✅ ContentExtractor")
    except ImportError as e:
        print(f"   ❌ ContentExtractor: {e}")
        return False

    try:
        from llm_analyzer import LLMAnalyzer
        print("   ✅ LLMAnalyzer")
    except ImportError as e:
        print(f"   ❌ LLMAnalyzer: {e}")
        return False

    try:
        from docx_generator import DocxGenerator
        print("   ✅ DocxGenerator")
    except ImportError as e:
        print(f"   ❌ DocxGenerator: {e}")
        return False

    try:
        from slide_processor import SlideProcessor
        print("   ✅ SlideProcessor")
    except ImportError as e:
        print(f"   ❌ SlideProcessor: {e}")
        return False

    return True


def test_configuration():
    """Test configuration loading."""
    print("\n🔍 Testing configuration...")

    try:
        import config

        print(f"   • Model: {config.MODEL_NAME}")
        print(f"   • Max tokens: {config.MAX_TOKENS}")
        print(f"   • Temperature: {config.TEMPERATURE}")
        print(f"   • Supported extensions: {config.SUPPORTED_EXTENSIONS}")

        if config.OPENROUTER_API_KEY:
            print("   ✅ API key configured")
        else:
            print("   ⚠️ API key not configured (set OPENROUTER_API_KEY)")

        return True
    except Exception as e:
        print(f"   ❌ Configuration error: {e}")
        return False


def test_basic_functionality():
    """Test basic functionality without API calls."""
    print("\n🔍 Testing basic functionality...")

    try:
        from content_extractor import ContentExtractor
        from docx_generator import DocxGenerator

        # Test ContentExtractor initialization
        extractor = ContentExtractor()
        print("   ✅ ContentExtractor initialization")

        # Test DocxGenerator initialization
        generator = DocxGenerator()
        print("   ✅ DocxGenerator initialization")

        # Test basic DOCX creation
        test_slides = [{
            'slide_number': 1,
            'title': 'Test Title',
            'subtitle': 'Test Subtitle',
            'body_text': [{'type': 'paragraph', 'content': 'Test content', 'style_hints': []}],
            'images': [],
            'layout_notes': 'Test slide'
        }]

        test_data = [{
            'slide_number': 1,
            'images': []
        }]

        # Create a temporary file path
        tmp_fd, tmp_path = tempfile.mkstemp(suffix='.docx')
        os.close(tmp_fd)  # Close the file descriptor to avoid locking issues

        try:
            output_path = generator.create_document(test_slides, test_data, tmp_path)

            if os.path.exists(output_path):
                print("   ✅ Basic DOCX generation")
            else:
                print("   ❌ DOCX generation failed")
                return False
        finally:
            # Clean up
            try:
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)
            except:
                pass  # Ignore cleanup errors

        return True
    except Exception as e:
        print(f"   ❌ Basic functionality test failed: {e}")
        return False


def test_api_connection():
    """Test API connection (if API key is available)."""
    print("\n🔍 Testing API connection...")

    try:
        import config

        if not config.OPENROUTER_API_KEY:
            print("   ⚠️ Skipping API test (no API key configured)")
            return True

        from llm_analyzer import LLMAnalyzer

        analyzer = LLMAnalyzer()

        # Test with minimal slide data
        test_slide = {
            'slide_number': 1,
            'raw_text': 'Test Slide\nThis is a test slide with some content.',
            'images': []
        }

        result = analyzer.analyze_slide(test_slide)

        if isinstance(result, dict) and 'slide_number' in result:
            print("   ✅ API connection successful")
            print(f"   • Response received with {len(result)} fields")
            return True
        else:
            print("   ❌ API returned unexpected response")
            return False

    except Exception as e:
        print(f"   ❌ API connection test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Running setup validation tests...\n")

    tests = [
        ("Import Tests", test_imports),
        ("Local Module Tests", test_local_modules),
        ("Configuration Tests", test_configuration),
        ("Basic Functionality Tests", test_basic_functionality),
        ("API Connection Tests", test_api_connection)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"{test_name}")
        print(f"{'='*50}")

        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

        print()

    # Summary
    print(f"{'='*50}")
    print("TEST SUMMARY")
    print(f"{'='*50}")

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Set your OpenRouter API key in .env file")
        print("2. Run: streamlit run main.py")
        print("3. Or use CLI: python cli.py --check-status")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please check the errors above.")
        print("\nCommon fixes:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check that all files are in the correct location")
        print("3. Verify your Python environment")


if __name__ == "__main__":
    main()
