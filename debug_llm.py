#!/usr/bin/env python3
"""
Debug LLM response directly.
"""

import os
import json
import requests
from dotenv import load_dotenv

load_dotenv()

def test_llm_direct():
    """Test LLM directly to see raw response."""
    
    api_key = os.getenv("OPENROUTER_API_KEY")
    model = "google/gemma-3-27b-it:free"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://github.com/your-repo",
        "X-Title": "Debug Test"
    }
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": """Analyze this slide content and provide a structured JSON response. 

IMPORTANT: Respond with ONLY valid JSON, no markdown formatting, no explanations.

Required JSON format:
{
    "slide_number": 1,
    "title": "main title text or null",
    "subtitle": "subtitle text or null", 
    "body_text": [
        {
            "type": "paragraph",
            "content": "text content here",
            "style_hints": []
        }
    ],
    "images": [],
    "layout_notes": "any observations"
}

Slide Content:
Test slide: Hello World
This is a test.

Image Information:
No images found on this slide.

Respond with valid JSON only:"""
            }
        ],
        "max_tokens": 500,
        "temperature": 0.1
    }
    
    try:
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            print("Raw LLM Response:")
            print("=" * 50)
            print(repr(content))
            print("=" * 50)
            print("Actual content:")
            print(content)
            print("=" * 50)
            
            # Test our parsing logic
            print("Testing parsing logic...")
            
            # Clean up the response
            cleaned = content.strip()
            
            # Handle various markdown code block formats
            if '```json' in cleaned:
                start = cleaned.find('```json') + 7
                end = cleaned.find('```', start)
                if end != -1:
                    cleaned = cleaned[start:end].strip()
                    print(f"Extracted from ```json: {repr(cleaned)}")
            elif '```' in cleaned:
                start = cleaned.find('```') + 3
                end = cleaned.find('```', start)
                if end != -1:
                    cleaned = cleaned[start:end].strip()
                    print(f"Extracted from ```: {repr(cleaned)}")
            
            # Remove any leading/trailing whitespace and newlines
            cleaned = cleaned.strip('\n\r\t ')
            print(f"After strip: {repr(cleaned)}")
            
            # Try to find JSON object in the response
            if not cleaned.startswith('{'):
                start_idx = cleaned.find('{')
                end_idx = cleaned.rfind('}')
                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    cleaned = cleaned[start_idx:end_idx + 1]
                    print(f"Extracted JSON object: {repr(cleaned)}")
            
            # Try to parse
            try:
                parsed = json.loads(cleaned)
                print("✅ Successfully parsed JSON!")
                print(json.dumps(parsed, indent=2))
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                print(f"Trying to parse: {repr(cleaned)}")
                
        else:
            print(f"API Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_llm_direct()
