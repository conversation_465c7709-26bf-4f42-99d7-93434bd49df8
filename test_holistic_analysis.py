#!/usr/bin/env python3
"""
Test the new holistic presentation analysis functionality.
"""

import os
import tempfile
from pptx import Presentation
from slide_processor import SlideProcessor


def create_test_presentation():
    """Create a test presentation with multiple related slides."""
    
    prs = Presentation()
    
    # Slide 1: Title slide
    slide1 = prs.slides.add_slide(prs.slide_layouts[0])
    title1 = slide1.shapes.title
    subtitle1 = slide1.placeholders[1]
    title1.text = "Machine Learning Fundamentals"
    subtitle1.text = "A Comprehensive Guide"
    
    # Slide 2: Introduction to ML
    slide2 = prs.slides.add_slide(prs.slide_layouts[1])
    title2 = slide2.shapes.title
    content2 = slide2.placeholders[1]
    title2.text = "What is Machine Learning?"
    content2.text = """Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.

Key characteristics:
• Learns from data
• Improves performance over time
• Makes predictions or decisions"""
    
    # Slide 3: Types of ML
    slide3 = prs.slides.add_slide(prs.slide_layouts[1])
    title3 = slide3.shapes.title
    content3 = slide3.placeholders[1]
    title3.text = "Types of Machine Learning"
    content3.text = """Supervised Learning
• Uses labeled training data
• Predicts outcomes for new data
• Examples: Classification, Regression

Unsupervised Learning
• Finds patterns in unlabeled data
• Discovers hidden structures
• Examples: Clustering, Dimensionality Reduction"""
    
    # Slide 4: Applications
    slide4 = prs.slides.add_slide(prs.slide_layouts[1])
    title4 = slide4.shapes.title
    content4 = slide4.placeholders[1]
    title4.text = "Real-World Applications"
    content4.text = """Healthcare
• Medical diagnosis
• Drug discovery
• Personalized treatment

Finance
• Fraud detection
• Algorithmic trading
• Credit scoring

Technology
• Recommendation systems
• Natural language processing
• Computer vision"""
    
    # Slide 5: Getting Started
    slide5 = prs.slides.add_slide(prs.slide_layouts[1])
    title5 = slide5.shapes.title
    content5 = slide5.placeholders[1]
    title5.text = "Getting Started with ML"
    content5.text = """Step 1: Learn the Fundamentals
• Statistics and probability
• Linear algebra
• Programming (Python/R)

Step 2: Choose Your Tools
• Scikit-learn for beginners
• TensorFlow for deep learning
• Jupyter notebooks for experimentation

Step 3: Practice with Projects
• Start with simple datasets
• Participate in competitions
• Build a portfolio"""
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as tmp:
        prs.save(tmp.name)
        return tmp.name


def test_holistic_analysis():
    """Test the holistic analysis functionality."""
    
    print("🧪 Testing Holistic Presentation Analysis")
    print("=" * 50)
    
    # Create test presentation
    print("📝 Creating test presentation...")
    test_file = create_test_presentation()
    
    try:
        # Initialize processor
        processor = SlideProcessor()
        
        # Test the new holistic processing
        print("🔄 Processing with holistic analysis...")
        output_path, stats = processor.process_file(test_file, "test_holistic_output.docx")
        
        print("\n✅ Processing completed!")
        print(f"📁 Output: {output_path}")
        
        # Display statistics
        print("\n📊 Processing Statistics:")
        for key, value in stats.items():
            print(f"   • {key}: {value}")
        
        # Check if output file exists and get size
        if os.path.exists(output_path):
            size = os.path.getsize(output_path)
            print(f"\n📄 Generated document: {size:,} bytes")
            
            # Verify it's the new format
            if stats.get('analysis_type') == 'holistic_presentation':
                print("✅ Confirmed: Using new holistic analysis!")
                print(f"🎯 Thematic sections created: {stats.get('total_sections', 0)}")
                print(f"📖 Document title: {stats.get('document_title', 'Unknown')}")
            else:
                print("⚠️ Warning: Still using legacy analysis")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.unlink(test_file)


def test_llm_presentation_analysis():
    """Test just the LLM presentation analysis component."""
    
    print("\n🧪 Testing LLM Presentation Analysis")
    print("=" * 50)
    
    try:
        from llm_analyzer import LLMAnalyzer
        
        analyzer = LLMAnalyzer()
        
        # Create mock slides data
        mock_slides = [
            {
                'slide_number': 1,
                'raw_text': 'Machine Learning Fundamentals\nA Comprehensive Guide',
                'images': []
            },
            {
                'slide_number': 2,
                'raw_text': 'What is Machine Learning?\nMachine learning enables computers to learn from data.\nKey benefits:\n• Automated insights\n• Improved accuracy\n• Scalable solutions',
                'images': []
            },
            {
                'slide_number': 3,
                'raw_text': 'Types of Machine Learning\nSupervised Learning\n• Uses labeled data\n• Predicts outcomes\nUnsupervised Learning\n• Finds patterns\n• No labels needed',
                'images': []
            }
        ]
        
        print("🤖 Analyzing presentation with LLM...")
        result = analyzer.analyze_presentation(mock_slides)
        
        print("✅ LLM analysis completed!")
        print(f"📖 Document title: {result.get('document_title', 'Unknown')}")
        print(f"🎯 Sections created: {len(result.get('sections', []))}")
        
        # Show section details
        sections = result.get('sections', [])
        for i, section in enumerate(sections, 1):
            print(f"\n📑 Section {i}: {section.get('section_title', 'Untitled')}")
            print(f"   Level: {section.get('heading_level', 1)}")
            print(f"   Content blocks: {len(section.get('content_blocks', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM analysis error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    
    print("🚀 Holistic Presentation Analysis Testing")
    print("=" * 60)
    
    # Test 1: LLM Analysis
    llm_success = test_llm_presentation_analysis()
    
    # Test 2: Full Processing
    full_success = test_holistic_analysis()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    print(f"🤖 LLM Analysis: {'✅ PASS' if llm_success else '❌ FAIL'}")
    print(f"🔄 Full Processing: {'✅ PASS' if full_success else '❌ FAIL'}")
    
    if llm_success and full_success:
        print("\n🎉 All tests passed! Holistic analysis is working correctly.")
        print("\n🎯 Key improvements:")
        print("   • Analyzes entire presentation as one unit")
        print("   • Creates thematic sections instead of slide-by-slide")
        print("   • Generates flowing textbook-style documents")
        print("   • Removes slide separators for seamless reading")
        print("   • Places images contextually within sections")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")


if __name__ == "__main__":
    main()
