#!/usr/bin/env python3
"""
Example usage of the Slides to Structured A4 PDF Converter.

This script demonstrates how to use the converter programmatically.
"""

import os
import tempfile
from pptx import Presentation
from pptx.util import Inches
from slide_processor import SlideProcessor


def create_sample_presentation():
    """Create a sample PPTX file for testing."""
    
    # Create a new presentation
    prs = Presentation()
    
    # Slide 1: Title slide
    slide1 = prs.slides.add_slide(prs.slide_layouts[0])  # Title slide layout
    title1 = slide1.shapes.title
    subtitle1 = slide1.placeholders[1]
    
    title1.text = "Sample Presentation"
    subtitle1.text = "Demonstrating AI-Powered Conversion"
    
    # Slide 2: Content slide
    slide2 = prs.slides.add_slide(prs.slide_layouts[1])  # Title and content layout
    title2 = slide2.shapes.title
    content2 = slide2.placeholders[1]
    
    title2.text = "Key Features"
    content2.text = """• AI-powered content analysis
• Automatic structure recognition
• Image extraction and placement
• Professional formatting
• Multi-format support"""
    
    # Slide 3: Another content slide
    slide3 = prs.slides.add_slide(prs.slide_layouts[1])
    title3 = slide3.shapes.title
    content3 = slide3.placeholders[1]
    
    title3.text = "Benefits"
    content3.text = """Time Saving
Eliminates manual copy-paste work

Consistency
Maintains professional formatting

Accuracy
AI understands content structure

Efficiency
Processes multiple slides quickly"""
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as tmp:
        prs.save(tmp.name)
        return tmp.name


def example_basic_usage():
    """Demonstrate basic usage of the converter."""
    
    print("🎯 Example 1: Basic Usage")
    print("=" * 40)
    
    # Create sample presentation
    print("📝 Creating sample presentation...")
    sample_file = create_sample_presentation()
    
    try:
        # Initialize processor
        processor = SlideProcessor()
        
        # Check if API key is configured
        validation = processor.validate_environment()
        if not validation.get('api_key_configured'):
            print("⚠️ API key not configured. This example will use fallback analysis.")
        
        # Process the file
        print("🔄 Processing presentation...")
        output_path, stats = processor.process_file(sample_file)
        
        # Display results
        print(f"✅ Processing complete!")
        print(f"📁 Output: {output_path}")
        print(f"📊 Stats: {stats}")
        
        # Check if output file exists
        if os.path.exists(output_path):
            print(f"📄 Generated document size: {os.path.getsize(output_path):,} bytes")
        
        return output_path
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        # Cleanup sample file
        if os.path.exists(sample_file):
            os.unlink(sample_file)


def example_custom_output():
    """Demonstrate usage with custom output path."""
    
    print("\n🎯 Example 2: Custom Output Path")
    print("=" * 40)
    
    # Create sample presentation
    sample_file = create_sample_presentation()
    custom_output = "my_structured_document.docx"
    
    try:
        processor = SlideProcessor()
        
        print(f"🔄 Processing with custom output: {custom_output}")
        output_path, stats = processor.process_file(sample_file, custom_output)
        
        print(f"✅ Custom output created: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    
    finally:
        if os.path.exists(sample_file):
            os.unlink(sample_file)


def example_component_testing():
    """Demonstrate testing individual components."""
    
    print("\n🎯 Example 3: Component Testing")
    print("=" * 40)
    
    try:
        # Test content extraction
        print("🔍 Testing content extraction...")
        from content_extractor import ContentExtractor
        
        extractor = ContentExtractor()
        sample_file = create_sample_presentation()
        
        slides_data = extractor.extract_from_pptx(sample_file)
        print(f"   ✅ Extracted {len(slides_data)} slides")
        
        for i, slide in enumerate(slides_data):
            print(f"   📄 Slide {i+1}: {len(slide['raw_text'])} characters")
        
        # Test LLM analyzer (if API key available)
        print("\n🤖 Testing LLM analyzer...")
        from llm_analyzer import LLMAnalyzer
        import config
        
        if config.OPENROUTER_API_KEY:
            analyzer = LLMAnalyzer()
            
            # Analyze first slide
            analysis = analyzer.analyze_slide(slides_data[0])
            print(f"   ✅ Analysis complete for slide 1")
            print(f"   📝 Title: {analysis.get('title', 'None')}")
            print(f"   📝 Subtitle: {analysis.get('subtitle', 'None')}")
            print(f"   📝 Body blocks: {len(analysis.get('body_text', []))}")
        else:
            print("   ⚠️ Skipping LLM test (no API key)")
        
        # Test DOCX generation
        print("\n📄 Testing DOCX generation...")
        from docx_generator import DocxGenerator
        
        generator = DocxGenerator()
        
        # Create mock analysis data
        mock_analysis = [{
            'slide_number': 1,
            'title': 'Test Title',
            'subtitle': 'Test Subtitle',
            'body_text': [
                {'type': 'paragraph', 'content': 'This is test content.', 'style_hints': []},
                {'type': 'list', 'content': 'Bullet point item', 'style_hints': ['bold']}
            ],
            'images': [],
            'layout_notes': 'Test layout'
        }]
        
        mock_data = [{'slide_number': 1, 'images': []}]
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp:
            output_path = generator.create_document(mock_analysis, mock_data, tmp.name)
            print(f"   ✅ Test document created: {output_path}")
            
            if os.path.exists(output_path):
                print(f"   📄 Document size: {os.path.getsize(output_path):,} bytes")
                os.unlink(output_path)
        
        # Cleanup
        if os.path.exists(sample_file):
            os.unlink(sample_file)
            
    except Exception as e:
        print(f"❌ Component testing error: {e}")


def main():
    """Run all examples."""
    
    print("🚀 Slides to Structured A4 PDF Converter")
    print("Example Usage Demonstrations")
    print("=" * 50)
    
    # Check environment
    print("🔧 Environment Check:")
    try:
        from slide_processor import SlideProcessor
        processor = SlideProcessor()
        validation = processor.validate_environment()
        
        for component, status in validation.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {component.replace('_', ' ').title()}")
        
        if not all(validation.values()):
            print("\n⚠️ Some components are not properly configured.")
            print("   The examples will still run but may use fallback methods.")
    
    except Exception as e:
        print(f"❌ Environment check failed: {e}")
        return
    
    print("\n" + "=" * 50)
    
    # Run examples
    output_files = []
    
    # Example 1: Basic usage
    output1 = example_basic_usage()
    if output1:
        output_files.append(output1)
    
    # Example 2: Custom output
    output2 = example_custom_output()
    if output2:
        output_files.append(output2)
    
    # Example 3: Component testing
    example_component_testing()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    
    if output_files:
        print("✅ Generated files:")
        for file_path in output_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"   📄 {file_path} ({size:,} bytes)")
            else:
                print(f"   ❌ {file_path} (file not found)")
        
        print(f"\n🎉 Successfully generated {len(output_files)} document(s)!")
        print("\nYou can open these files in Microsoft Word or any compatible application.")
        
        # Cleanup option
        cleanup = input("\n🗑️ Delete generated files? (y/N): ").lower().strip()
        if cleanup == 'y':
            for file_path in output_files:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    print(f"   🗑️ Deleted: {file_path}")
    else:
        print("❌ No files were generated successfully.")
        print("   Check the error messages above for troubleshooting.")


if __name__ == "__main__":
    main()
