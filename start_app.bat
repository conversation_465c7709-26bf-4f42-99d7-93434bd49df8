@echo off
echo.
echo ========================================
echo  Slides to Structured A4 PDF Converter
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "main.py" (
    echo ERROR: main.py not found
    echo Please run this script from the project directory
    pause
    exit /b 1
)

REM Check if dependencies are installed
echo Checking dependencies...
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Check if .env file exists
if not exist ".env" (
    echo.
    echo WARNING: .env file not found
    echo Please create .env file with your OpenRouter API key
    echo You can copy .env.example and edit it
    echo.
    pause
)

REM Start the application
echo.
echo Starting Streamlit application...
echo The app will open in your default browser
echo Press Ctrl+C to stop the application
echo.

streamlit run main.py

pause
