import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
MODEL_NAME = os.getenv("MODEL_NAME", "google/gemma-3-27b-it:free")
MAX_TOKENS = int(os.getenv("MAX_TOKENS", "4000"))
TEMPERATURE = float(os.getenv("TEMPERATURE", "0.1"))

# OpenRouter API endpoint
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"

# Supported file types
SUPPORTED_EXTENSIONS = ['.pptx', '.pdf']

# Output settings
DEFAULT_OUTPUT_SUFFIX = "_structured_A4"

# LLM Prompt Templates
SLIDE_ANALYSIS_PROMPT = """
Analyze this slide content and provide a structured JSON response.

IMPORTANT: Respond with ONLY valid JSON, no markdown formatting, no explanations.

Required JSON format:
{{
    "slide_number": 1,
    "title": "main title text or null",
    "subtitle": "subtitle text or null",
    "body_text": [
        {{
            "type": "paragraph",
            "content": "text content here",
            "style_hints": []
        }}
    ],
    "images": [
        {{
            "position": "after_text",
            "context": "description of image",
            "image_index": 0
        }}
    ],
    "layout_notes": "any observations"
}}

Slide Content:
{slide_content}

Image Information:
{image_info}

Respond with valid JSON only:"""
