import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
MODEL_NAME = os.getenv("MODEL_NAME", "google/gemma-3-27b-it:free")
MAX_TOKENS = int(os.getenv("MAX_TOKENS", "4000"))
TEMPERATURE = float(os.getenv("TEMPERATURE", "0.1"))

# OpenRouter API endpoint
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"

# Supported file types
SUPPORTED_EXTENSIONS = ['.pptx', '.pdf']

# Output settings
DEFAULT_OUTPUT_SUFFIX = "_structured_A4"

# LLM Prompt Templates
PRESENTATION_ANALYSIS_PROMPT = """
Analyze this complete presentation and restructure it into a cohesive textbook-style document.

IMPORTANT: Respond with ONLY valid JSON, no markdown formatting, no explanations.

Your task:
1. Identify the main themes and topics across all slides
2. Group related content from different slides into logical sections
3. Create a flowing narrative structure with proper heading hierarchy
4. Preserve ALL original content - do not add or remove any text
5. Use original titles from slides as section headings where appropriate
6. Organize images contextually within relevant sections

Required JSON format:
{{
    "document_title": "main presentation title",
    "sections": [
        {{
            "section_title": "section heading from original content",
            "heading_level": 1,
            "content_blocks": [
                {{
                    "type": "paragraph",
                    "content": "original text content",
                    "style_hints": ["bold", "italic"]
                }},
                {{
                    "type": "list",
                    "items": ["item 1", "item 2"],
                    "list_type": "bullet"
                }},
                {{
                    "type": "image",
                    "image_index": 0,
                    "context": "brief context for placement"
                }}
            ]
        }}
    ],
    "content_flow_notes": "explanation of how content was reorganized"
}}

Complete Presentation Content:
{presentation_content}

All Images Information:
{all_images_info}

Respond with valid JSON only:"""
