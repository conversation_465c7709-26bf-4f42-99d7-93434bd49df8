import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
MODEL_NAME = os.getenv("MODEL_NAME", "google/gemma-3-27b-it:free")
MAX_TOKENS = int(os.getenv("MAX_TOKENS", "4000"))
TEMPERATURE = float(os.getenv("TEMPERATURE", "0.1"))

# OpenRouter API endpoint
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"

# Supported file types
SUPPORTED_EXTENSIONS = ['.pptx', '.pdf']

# Output settings
DEFAULT_OUTPUT_SUFFIX = "_structured_A4"

# LLM Prompt Templates
PRESENTATION_ANALYSIS_PROMPT = """
Analyze this presentation and create a structured document. Respond with ONLY valid JSON.

Task: Group related content into logical sections with proper headings.

JSON format:
{{
    "document_title": "presentation title",
    "sections": [
        {{
            "section_title": "section name",
            "heading_level": 1,
            "content_blocks": [
                {{
                    "type": "paragraph",
                    "content": "text content"
                }}
            ]
        }}
    ]
}}

Content:
{presentation_content}

Images:
{all_images_info}

JSON:"""
