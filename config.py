import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API Configuration
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
MODEL_NAME = os.getenv("MODEL_NAME", "google/gemma-3-27b-it:free")
MAX_TOKENS = int(os.getenv("MAX_TOKENS", "4000"))
TEMPERATURE = float(os.getenv("TEMPERATURE", "0.1"))

# OpenRouter API endpoint
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"

# Supported file types
SUPPORTED_EXTENSIONS = ['.pptx', '.pdf']

# Output settings
DEFAULT_OUTPUT_SUFFIX = "_structured_A4"

# LLM Prompt Templates
SLIDE_ANALYSIS_PROMPT = """
Analyze this slide content and provide a structured JSON response with the following format:

{
    "slide_number": <number>,
    "title": "<main title text or null if none>",
    "subtitle": "<subtitle text or null if none>",
    "body_text": [
        {
            "type": "paragraph|list|bullet_point",
            "content": "<text content>",
            "style_hints": ["bold", "italic", "emphasis"] // if applicable
        }
    ],
    "images": [
        {
            "position": "before_text|after_text|between_paragraphs",
            "context": "<description of what this image relates to>",
            "image_index": <0-based index of image on this slide>
        }
    ],
    "layout_notes": "<any important layout or formatting observations>"
}

Please analyze the following slide content and respond with ONLY the JSON structure:

Slide Content:
{slide_content}

Image Information:
{image_info}
"""
