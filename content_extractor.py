import io
import base64
from typing import List, Dict, Any, Tuple
from pptx import Presentation
from pptx.shapes.picture import Picture
import fitz  # PyMuPDF
from PIL import Image


class ContentExtractor:
    """Extract text and images from PPTX and PDF files."""
    
    def __init__(self):
        pass
    
    def extract_from_pptx(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract content from PPTX file."""
        presentation = Presentation(file_path)
        slides_data = []
        
        for slide_idx, slide in enumerate(presentation.slides):
            slide_data = {
                'slide_number': slide_idx + 1,
                'text_content': self._extract_text_from_slide(slide),
                'images': self._extract_images_from_slide(slide, slide_idx),
                'raw_text': self._get_all_text_from_slide(slide)
            }
            slides_data.append(slide_data)
        
        return slides_data
    
    def extract_from_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract content from PDF file."""
        doc = fitz.open(file_path)
        slides_data = []
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            
            # Extract text
            text_content = page.get_text()
            
            # Extract images
            images = self._extract_images_from_pdf_page(page, page_num)
            
            slide_data = {
                'slide_number': page_num + 1,
                'text_content': text_content,
                'images': images,
                'raw_text': text_content
            }
            slides_data.append(slide_data)
        
        doc.close()
        return slides_data
    
    def _extract_text_from_slide(self, slide) -> str:
        """Extract formatted text from a PPTX slide."""
        text_runs = []
        
        for shape in slide.shapes:
            if hasattr(shape, "text"):
                text_runs.append(shape.text)
        
        return "\n".join(text_runs)
    
    def _get_all_text_from_slide(self, slide) -> str:
        """Get all text from slide for LLM analysis."""
        all_text = []
        
        for shape in slide.shapes:
            if hasattr(shape, "text") and shape.text.strip():
                all_text.append(f"Text block: {shape.text.strip()}")
        
        return "\n".join(all_text)
    
    def _extract_images_from_slide(self, slide, slide_idx: int) -> List[Dict[str, Any]]:
        """Extract images from a PPTX slide."""
        images = []
        image_idx = 0
        
        for shape in slide.shapes:
            if isinstance(shape, Picture):
                try:
                    # Get image data
                    image_part = shape.image.blob
                    
                    # Convert to base64 for storage/transmission
                    image_b64 = base64.b64encode(image_part).decode('utf-8')
                    
                    images.append({
                        'image_index': image_idx,
                        'slide_index': slide_idx,
                        'format': shape.image.ext,
                        'data': image_b64,
                        'size': len(image_part)
                    })
                    image_idx += 1
                except Exception as e:
                    print(f"Error extracting image from slide {slide_idx}: {e}")
        
        return images
    
    def _extract_images_from_pdf_page(self, page, page_num: int) -> List[Dict[str, Any]]:
        """Extract images from a PDF page."""
        images = []
        image_list = page.get_images()
        
        for image_idx, img in enumerate(image_list):
            try:
                xref = img[0]
                pix = fitz.Pixmap(page.parent, xref)
                
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_data = pix.tobytes("png")
                    image_b64 = base64.b64encode(img_data).decode('utf-8')
                    
                    images.append({
                        'image_index': image_idx,
                        'slide_index': page_num,
                        'format': 'png',
                        'data': image_b64,
                        'size': len(img_data)
                    })
                
                pix = None
            except Exception as e:
                print(f"Error extracting image from page {page_num}: {e}")
        
        return images
