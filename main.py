import streamlit as st
import os
import tempfile
from slide_processor import SlideProcessor
import config


def main():
    """Main Streamlit application."""
    
    st.set_page_config(
        page_title="Slides to Structured A4 PDF Converter",
        page_icon="📄",
        layout="wide"
    )
    
    st.title("📄 Slides to Structured A4 PDF Converter")
    st.markdown("Convert your presentation slides (PPTX/PDF) into a structured Word document using AI analysis.")
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("Configuration")
        
        # API Key input
        api_key = st.text_input(
            "OpenRouter API Key",
            type="password",
            value=config.OPENROUTER_API_KEY or "",
            help="Enter your OpenRouter API key"
        )
        
        if api_key:
            os.environ["OPENROUTER_API_KEY"] = api_key
            config.OPENROUTER_API_KEY = api_key
        
        # Model selection
        model_name = st.selectbox(
            "Model",
            ["google/gemini-2.5-flash-preview-05-20", "google/gemini-pro", "anthropic/claude-3-sonnet"],
            index=0,
            help="Select the LLM model to use for analysis"
        )
        config.MODEL_NAME = model_name
        
        # Advanced settings
        with st.expander("Advanced Settings"):
            max_tokens = st.slider("Max Tokens", 1000, 8000, config.MAX_TOKENS)
            temperature = st.slider("Temperature", 0.0, 1.0, config.TEMPERATURE, 0.1)
            config.MAX_TOKENS = max_tokens
            config.TEMPERATURE = temperature
    
    # Main interface
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("Upload Your Presentation")
        
        uploaded_file = st.file_uploader(
            "Choose a PPTX or PDF file",
            type=['pptx', 'pdf'],
            help="Upload your presentation file to convert"
        )
        
        if uploaded_file is not None:
            # Display file info
            st.info(f"📁 File: {uploaded_file.name} ({uploaded_file.size:,} bytes)")
            
            # Process button
            if st.button("🚀 Convert to Structured Document", type="primary"):
                if not config.OPENROUTER_API_KEY:
                    st.error("❌ Please enter your OpenRouter API key in the sidebar.")
                    return
                
                process_file(uploaded_file)
    
    with col2:
        st.header("How it works")
        st.markdown("""
        1. **Upload** your PPTX or PDF presentation
        2. **AI Analysis** extracts and structures content
        3. **Generate** a formatted Word document
        4. **Download** your structured A4 document
        
        ### Features:
        - ✅ Preserves titles and subtitles
        - ✅ Maintains text hierarchy
        - ✅ Includes images with context
        - ✅ Sequential slide-to-section mapping
        - ✅ Professional formatting
        """)
        
        # System status
        if st.button("🔍 Check System Status"):
            check_system_status()


def process_file(uploaded_file):
    """Process the uploaded file."""
    
    try:
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(uploaded_file.name)[1]) as tmp_file:
            tmp_file.write(uploaded_file.getvalue())
            tmp_file_path = tmp_file.name
        
        # Initialize processor
        processor = SlideProcessor()
        
        # Create progress bar
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Process file
        status_text.text("🔄 Initializing processor...")
        progress_bar.progress(10)
        
        status_text.text("📖 Extracting content from slides...")
        progress_bar.progress(30)
        
        status_text.text("🤖 Analyzing content with AI...")
        progress_bar.progress(60)
        
        # Generate output filename
        base_name = os.path.splitext(uploaded_file.name)[0]
        output_filename = f"{base_name}_structured_A4.docx"
        
        status_text.text("📝 Generating structured document...")
        progress_bar.progress(80)
        
        # Process the file
        output_path, stats = processor.process_file(tmp_file_path, output_filename)
        
        progress_bar.progress(100)
        status_text.text("✅ Processing complete!")
        
        # Display results
        st.success("🎉 Conversion completed successfully!")
        
        # Show statistics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Slides Processed", stats.get('total_slides', 0))
        with col2:
            st.metric("Images Found", stats.get('total_images', 0))
        with col3:
            st.metric("Text Blocks", stats.get('total_text_blocks', 0))
        
        # Download button
        if os.path.exists(output_path):
            with open(output_path, 'rb') as file:
                st.download_button(
                    label="📥 Download Structured Document",
                    data=file.read(),
                    file_name=output_filename,
                    mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                )
        
        # Cleanup
        os.unlink(tmp_file_path)
        if os.path.exists(output_path):
            os.unlink(output_path)
            
    except Exception as e:
        st.error(f"❌ Error processing file: {str(e)}")
        st.exception(e)


def check_system_status():
    """Check and display system status."""
    
    try:
        processor = SlideProcessor()
        validation = processor.validate_environment()
        
        st.subheader("System Status")
        
        for component, status in validation.items():
            if status:
                st.success(f"✅ {component.replace('_', ' ').title()}")
            else:
                st.error(f"❌ {component.replace('_', ' ').title()}")
        
        if all(validation.values()):
            st.success("🎉 All systems operational!")
        else:
            st.warning("⚠️ Some components need attention.")
            
    except Exception as e:
        st.error(f"❌ Error checking system status: {str(e)}")


if __name__ == "__main__":
    main()
