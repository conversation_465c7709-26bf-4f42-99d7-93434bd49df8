import json
import requests
from typing import Dict, Any, List
import config


class LLMAnalyzer:
    """Analyze slide content using Google Gemini via OpenRouter."""
    
    def __init__(self):
        self.api_key = config.OPENROUTER_API_KEY
        self.model_name = config.MODEL_NAME
        self.base_url = config.OPENROUTER_BASE_URL
        
        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY not found in environment variables")
    
    def analyze_slide(self, slide_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a single slide's content structure."""
        
        # Prepare slide content for analysis
        slide_content = slide_data.get('raw_text', '')
        image_info = self._format_image_info(slide_data.get('images', []))
        
        # Create the prompt
        prompt = config.SLIDE_ANALYSIS_PROMPT.format(
            slide_content=slide_content,
            image_info=image_info
        )
        
        try:
            response = self._call_llm(prompt)
            return self._parse_llm_response(response, slide_data['slide_number'])
        except Exception as e:
            print(f"Error analyzing slide {slide_data['slide_number']}: {e}")
            return self._create_fallback_analysis(slide_data)
    
    def _call_llm(self, prompt: str) -> str:
        """Make API call to OpenRouter."""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/your-repo",  # Optional
            "X-Title": "Slides to A4 Converter"  # Optional
        }
        
        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": config.MAX_TOKENS,
            "temperature": config.TEMPERATURE
        }
        
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code != 200:
            raise Exception(f"API call failed: {response.status_code} - {response.text}")
        
        result = response.json()
        return result['choices'][0]['message']['content']
    
    def _parse_llm_response(self, response: str, slide_number: int) -> Dict[str, Any]:
        """Parse the LLM's JSON response."""
        try:
            # Try to extract JSON from the response
            response = response.strip()
            
            # Handle cases where LLM might wrap JSON in markdown
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            
            parsed = json.loads(response)
            
            # Ensure slide_number is set correctly
            parsed['slide_number'] = slide_number
            
            return parsed
        except json.JSONDecodeError as e:
            print(f"Failed to parse LLM response as JSON: {e}")
            print(f"Response was: {response}")
            return self._create_fallback_analysis({'slide_number': slide_number})
    
    def _format_image_info(self, images: List[Dict[str, Any]]) -> str:
        """Format image information for the LLM prompt."""
        if not images:
            return "No images found on this slide."
        
        image_descriptions = []
        for i, img in enumerate(images):
            image_descriptions.append(f"Image {i}: Format={img.get('format', 'unknown')}, Size={img.get('size', 0)} bytes")
        
        return "\n".join(image_descriptions)
    
    def _create_fallback_analysis(self, slide_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a basic analysis when LLM fails."""
        raw_text = slide_data.get('raw_text', '')
        lines = [line.strip() for line in raw_text.split('\n') if line.strip()]
        
        # Simple heuristic: first line is likely title
        title = lines[0] if lines else None
        subtitle = lines[1] if len(lines) > 1 and len(lines[1]) < 100 else None
        
        body_text = []
        start_idx = 2 if subtitle else 1
        
        for line in lines[start_idx:]:
            body_text.append({
                "type": "paragraph",
                "content": line,
                "style_hints": []
            })
        
        images = []
        for i, img in enumerate(slide_data.get('images', [])):
            images.append({
                "position": "after_text",
                "context": f"Image {i+1} from slide",
                "image_index": i
            })
        
        return {
            "slide_number": slide_data['slide_number'],
            "title": title,
            "subtitle": subtitle,
            "body_text": body_text,
            "images": images,
            "layout_notes": "Fallback analysis - LLM processing failed"
        }
