import json
import requests
from typing import Dict, Any, List
import config


class LLMAnalyzer:
    """Analyze slide content using Google Gemini via OpenRouter."""

    def __init__(self):
        self.api_key = config.OPENROUTER_API_KEY
        self.model_name = config.MODEL_NAME
        self.base_url = config.OPENROUTER_BASE_URL

        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY not found in environment variables")

    def analyze_presentation(self, all_slides_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze the complete presentation holistically."""

        # Prepare complete presentation content
        presentation_content = self._format_presentation_content(all_slides_data)
        all_images_info = self._format_all_images_info(all_slides_data)

        # Create the prompt
        prompt = config.PRESENTATION_ANALYSIS_PROMPT.format(
            presentation_content=presentation_content,
            all_images_info=all_images_info
        )

        try:
            response = self._call_llm(prompt)
            return self._parse_presentation_response(response)
        except Exception as e:
            print(f"Error analyzing presentation: {e}")
            return self._create_fallback_presentation_analysis(all_slides_data)

    def analyze_slide(self, slide_data: Dict[str, Any]) -> Dict[str, Any]:
        """Legacy method for single slide analysis - kept for compatibility."""

        # For single slide, create a mini-presentation analysis
        return self.analyze_presentation([slide_data])

    def _call_llm(self, prompt: str) -> str:
        """Make API call to OpenRouter."""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/your-repo",  # Optional
            "X-Title": "Slides to A4 Converter"  # Optional
        }

        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": config.MAX_TOKENS,
            "temperature": config.TEMPERATURE
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )

        if response.status_code != 200:
            raise Exception(f"API call failed: {response.status_code} - {response.text}")

        result = response.json()
        return result['choices'][0]['message']['content']

    def _format_presentation_content(self, all_slides_data: List[Dict[str, Any]]) -> str:
        """Format all slides content for holistic analysis."""
        content_parts = []

        for i, slide_data in enumerate(all_slides_data, 1):
            slide_text = slide_data.get('raw_text', '').strip()
            if slide_text:
                content_parts.append(f"SLIDE {i}:\n{slide_text}")

        return "\n\n".join(content_parts)

    def _format_all_images_info(self, all_slides_data: List[Dict[str, Any]]) -> str:
        """Format all images information for holistic analysis."""
        all_images = []
        image_index = 0

        for slide_idx, slide_data in enumerate(all_slides_data, 1):
            slide_images = slide_data.get('images', [])
            for img in slide_images:
                all_images.append(f"Image {image_index}: From slide {slide_idx}, Format={img.get('format', 'unknown')}, Size={img.get('size', 0)} bytes")
                image_index += 1

        return "\n".join(all_images) if all_images else "No images found in presentation."

    def _parse_presentation_response(self, response: str) -> Dict[str, Any]:
        """Parse the LLM's presentation analysis response."""
        try:
            # Clean up the response (same logic as before)
            response = response.strip()

            # Handle various markdown code block formats
            if '```json' in response:
                start = response.find('```json') + 7
                end = response.find('```', start)
                if end != -1:
                    response = response[start:end].strip()
            elif '```' in response:
                start = response.find('```') + 3
                end = response.find('```', start)
                if end != -1:
                    response = response[start:end].strip()

            # Remove any leading/trailing whitespace and newlines
            response = response.strip('\n\r\t ')

            # Try to find JSON object in the response
            if not response.startswith('{'):
                start_idx = response.find('{')
                end_idx = response.rfind('}')
                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    response = response[start_idx:end_idx + 1]

            parsed = json.loads(response)
            return parsed

        except json.JSONDecodeError as e:
            print(f"Failed to parse presentation analysis as JSON: {e}")
            print(f"Original response length: {len(response)}")
            print(f"Original response: '{response}'")
            return self._create_fallback_presentation_analysis([])

    def _parse_llm_response(self, response: str, slide_number: int) -> Dict[str, Any]:
        """Parse the LLM's JSON response."""
        try:
            # Clean up the response
            response = response.strip()

            # Handle various markdown code block formats
            if '```json' in response:
                # Extract content between ```json and ```
                start = response.find('```json') + 7
                end = response.find('```', start)
                if end != -1:
                    response = response[start:end].strip()
            elif '```' in response:
                # Extract content between ``` and ```
                start = response.find('```') + 3
                end = response.find('```', start)
                if end != -1:
                    response = response[start:end].strip()

            # Remove any leading/trailing whitespace and newlines
            response = response.strip('\n\r\t ')

            # Try to find JSON object in the response
            if not response.startswith('{'):
                # Look for the first { and last }
                start_idx = response.find('{')
                end_idx = response.rfind('}')
                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    response = response[start_idx:end_idx + 1]

            parsed = json.loads(response)

            # Ensure slide_number is set correctly
            parsed['slide_number'] = slide_number

            return parsed
        except json.JSONDecodeError as e:
            print(f"Failed to parse LLM response as JSON: {e}")
            print(f"Original response length: {len(response)}")
            print(f"Original response: '{response}'")
            print(f"Response repr: {repr(response)}")
            return self._create_fallback_analysis({'slide_number': slide_number})

    def _format_image_info(self, images: List[Dict[str, Any]]) -> str:
        """Format image information for the LLM prompt."""
        if not images:
            return "No images found on this slide."

        image_descriptions = []
        for i, img in enumerate(images):
            image_descriptions.append(f"Image {i}: Format={img.get('format', 'unknown')}, Size={img.get('size', 0)} bytes")

        return "\n".join(image_descriptions)

    def _create_fallback_analysis(self, slide_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a basic analysis when LLM fails."""
        raw_text = slide_data.get('raw_text', '')
        lines = [line.strip() for line in raw_text.split('\n') if line.strip()]

        # Simple heuristic: first line is likely title
        title = lines[0] if lines else None
        subtitle = lines[1] if len(lines) > 1 and len(lines[1]) < 100 else None

        body_text = []
        start_idx = 2 if subtitle else 1

        for line in lines[start_idx:]:
            body_text.append({
                "type": "paragraph",
                "content": line,
                "style_hints": []
            })

        images = []
        for i, img in enumerate(slide_data.get('images', [])):
            images.append({
                "position": "after_text",
                "context": f"Image {i+1} from slide",
                "image_index": i
            })

        return {
            "slide_number": slide_data['slide_number'],
            "title": title,
            "subtitle": subtitle,
            "body_text": body_text,
            "images": images,
            "layout_notes": "Fallback analysis - LLM processing failed"
        }

    def _create_fallback_presentation_analysis(self, all_slides_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a basic presentation analysis when LLM fails."""

        # Extract all content and create basic structure
        sections = []
        all_images = []
        image_index = 0

        # Collect all images first
        for slide_data in all_slides_data:
            for img in slide_data.get('images', []):
                all_images.append({
                    'original_slide': slide_data['slide_number'],
                    'image_data': img
                })

        # Create sections from slides
        for slide_data in all_slides_data:
            raw_text = slide_data.get('raw_text', '')
            lines = [line.strip() for line in raw_text.split('\n') if line.strip()]

            if not lines:
                continue

            # Use first line as section title, rest as content
            section_title = lines[0] if lines else f"Section {slide_data['slide_number']}"
            content_blocks = []

            # Add text content
            for line in lines[1:]:
                content_blocks.append({
                    "type": "paragraph",
                    "content": line,
                    "style_hints": []
                })

            # Add images from this slide
            slide_images = slide_data.get('images', [])
            for i, img in enumerate(slide_images):
                content_blocks.append({
                    "type": "image",
                    "image_index": image_index,
                    "context": f"Image from original slide {slide_data['slide_number']}"
                })
                image_index += 1

            sections.append({
                "section_title": section_title,
                "heading_level": 1,
                "content_blocks": content_blocks
            })

        # Determine document title
        document_title = "Presentation Content"
        if sections and sections[0]["content_blocks"]:
            first_content = sections[0]["content_blocks"][0]
            if first_content["type"] == "paragraph":
                document_title = first_content["content"][:50] + "..." if len(first_content["content"]) > 50 else first_content["content"]

        return {
            "document_title": document_title,
            "sections": sections,
            "content_flow_notes": "Fallback analysis - content organized by original slide order"
        }
