# Slides to Structured A4 PDF Converter

An AI-powered tool that converts presentation slides (PPTX/PDF) into structured Word documents using Google Gemini 2.5 Flash via OpenRouter API.

## Features

- 📄 **Multi-format Support**: Accepts both PPTX and PDF presentation files
- 🤖 **AI-Powered Analysis**: Uses Google Gemini 2.5 Flash to understand slide structure
- 📝 **Structured Output**: Generates well-formatted Word documents with proper hierarchy
- 🖼️ **Image Preservation**: Extracts and places images with contextual information
- 🎨 **Professional Formatting**: Maintains titles, subtitles, and text formatting
- 🌐 **Web Interface**: Easy-to-use Streamlit interface

## Quick Start

### 1. Installation

```bash
# Clone or download the project
cd courstest

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

1. Copy the environment template:
```bash
copy .env.example .env
```

2. Edit `.env` and add your OpenRouter API key:
```
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### 3. Get OpenRouter API Key

1. Visit [OpenRouter](https://openrouter.ai/)
2. Sign up for an account
3. Generate an API key
4. Add credits to your account (Google Gemini 2.5 Flash is very cost-effective)

### 4. Run the Application

```bash
streamlit run main.py
```

The web interface will open in your browser at `http://localhost:8501`

## Usage

1. **Upload File**: Choose a PPTX or PDF presentation file
2. **Configure**: Enter your OpenRouter API key in the sidebar
3. **Convert**: Click "Convert to Structured Document"
4. **Download**: Get your structured Word document

## How It Works

### Processing Pipeline

1. **Content Extraction**
   - PPTX: Uses `python-pptx` to extract text and images
   - PDF: Uses `PyMuPDF` to extract content from each page

2. **AI Analysis**
   - Sends slide content to Google Gemini 2.5 Flash
   - Identifies titles, subtitles, body text, and image contexts
   - Provides structured JSON analysis for each slide

3. **Document Generation**
   - Uses `python-docx` to create formatted Word document
   - Applies proper heading styles and text formatting
   - Inserts images with contextual placement

### File Structure

```
courstest/
├── main.py                 # Streamlit web interface
├── slide_processor.py      # Main processing orchestrator
├── content_extractor.py    # Extract content from PPTX/PDF
├── llm_analyzer.py        # LLM integration for content analysis
├── docx_generator.py      # Generate structured DOCX output
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
└── README.md             # This file
```

## Configuration Options

### Environment Variables

- `OPENROUTER_API_KEY`: Your OpenRouter API key (required)
- `MODEL_NAME`: LLM model to use (default: google/gemini-2.5-flash-preview-05-20)
- `MAX_TOKENS`: Maximum tokens for LLM response (default: 4000)
- `TEMPERATURE`: LLM temperature setting (default: 0.1)

### Supported File Types

- `.pptx` - PowerPoint presentations
- `.pdf` - PDF files (optimized for slide-based PDFs)

## Cost Considerations

Google Gemini 2.5 Flash via OpenRouter is very cost-effective:
- Typical cost: ~$0.01-0.05 per presentation
- Fast processing: Usually completes in 10-30 seconds
- High quality: Excellent content structure recognition

## Troubleshooting

### Common Issues

1. **API Key Error**
   - Ensure your OpenRouter API key is correctly set
   - Check that you have credits in your OpenRouter account

2. **File Processing Error**
   - Verify file is not corrupted
   - Ensure file is a valid PPTX or PDF
   - Check file size (very large files may timeout)

3. **Image Extraction Issues**
   - Some PDF files may have embedded images that are difficult to extract
   - PPTX files generally have better image extraction success

### System Status Check

Use the "Check System Status" button in the web interface to verify:
- API key configuration
- LLM connection
- All components are working properly

## Development

### Adding New Features

The modular design makes it easy to extend:

- **New file formats**: Extend `ContentExtractor`
- **Different LLMs**: Modify `LLMAnalyzer`
- **Output formats**: Create new generators alongside `DocxGenerator`
- **UI improvements**: Enhance `main.py`

### Testing

```bash
# Test with a sample file
python -c "
from slide_processor import SlideProcessor
processor = SlideProcessor()
output, stats = processor.process_file('sample.pptx')
print(f'Generated: {output}')
print(f'Stats: {stats}')
"
```

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify your OpenRouter API key and credits
3. Test with a simple presentation file first
