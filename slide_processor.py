import os
from typing import List, Dict, <PERSON>, <PERSON><PERSON>
from content_extractor import ContentExtractor
from llm_analyzer import LLMAnaly<PERSON>
from docx_generator import DocxGenerator
import config


class SlideProcessor:
    """Main processor for converting slides to structured DOCX."""

    def __init__(self):
        self.content_extractor = ContentExtractor()
        self.llm_analyzer = LLMAnalyzer()
        self.docx_generator = DocxGenerator()

    def process_file(self, input_path: str, output_path: str = None) -> Tuple[str, Dict[str, Any]]:
        """
        Process a presentation file and generate structured DOCX.

        Args:
            input_path: Path to input PPTX or PDF file
            output_path: Path for output DOCX file (optional)

        Returns:
            Tuple of (output_file_path, processing_stats)
        """

        # Validate input file
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")

        file_ext = os.path.splitext(input_path)[1].lower()
        if file_ext not in config.SUPPORTED_EXTENSIONS:
            raise ValueError(f"Unsupported file type: {file_ext}")

        # Generate output path if not provided
        if output_path is None:
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            output_path = f"{base_name}{config.DEFAULT_OUTPUT_SUFFIX}.docx"

        # Step 1: Extract content from slides
        print("Extracting content from slides...")
        if file_ext == '.pptx':
            slides_data = self.content_extractor.extract_from_pptx(input_path)
        elif file_ext == '.pdf':
            slides_data = self.content_extractor.extract_from_pdf(input_path)

        print(f"Extracted content from {len(slides_data)} slides")

        # Step 2: Analyze content structure with LLM
        print("Analyzing slide structure with LLM...")
        slides_analysis = []

        for i, slide_data in enumerate(slides_data):
            print(f"Analyzing slide {i + 1}/{len(slides_data)}")
            analysis = self.llm_analyzer.analyze_slide(slide_data)
            slides_analysis.append(analysis)

        # Step 3: Generate DOCX document
        print("Generating structured DOCX document...")
        final_output_path = self.docx_generator.create_document(
            slides_analysis, slides_data, output_path
        )

        # Compile processing statistics
        stats = self._compile_stats(slides_data, slides_analysis)
        stats.update(self.docx_generator.get_document_stats())

        print(f"Processing complete! Output saved to: {final_output_path}")
        return final_output_path, stats

    def _compile_stats(self, slides_data: List[Dict[str, Any]],
                      slides_analysis: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Compile processing statistics."""

        total_slides = len(slides_data)
        total_images = sum(len(slide.get('images', [])) for slide in slides_data)

        # Count text blocks
        total_text_blocks = 0
        titles_found = 0
        subtitles_found = 0

        for analysis in slides_analysis:
            if analysis.get('title'):
                titles_found += 1
            if analysis.get('subtitle'):
                subtitles_found += 1
            total_text_blocks += len(analysis.get('body_text', []))

        return {
            'total_slides': total_slides,
            'total_images': total_images,
            'total_text_blocks': total_text_blocks,
            'titles_found': titles_found,
            'subtitles_found': subtitles_found,
            'processing_success': True
        }

    def validate_environment(self) -> Dict[str, bool]:
        """Validate that all required components are properly configured."""
        validation = {
            'api_key_configured': bool(config.OPENROUTER_API_KEY),
            'model_configured': bool(config.MODEL_NAME),
            'content_extractor_ready': True,
            'docx_generator_ready': True
        }

        # Test LLM connection (basic validation without API call)
        try:
            # Check if LLM analyzer can be initialized properly
            validation['llm_connection'] = (
                bool(self.llm_analyzer.api_key) and
                bool(self.llm_analyzer.model_name) and
                self.llm_analyzer.api_key.startswith('sk-or-')
            )
        except Exception as e:
            print(f"LLM connection test failed: {e}")
            validation['llm_connection'] = False

        return validation

    def test_llm_connection(self) -> Dict[str, Any]:
        """Test actual LLM connection with a real API call."""
        try:
            test_slide = {
                'slide_number': 1,
                'raw_text': 'Test slide: Hello World\nThis is a test.',
                'images': []
            }
            result = self.llm_analyzer.analyze_slide(test_slide)

            return {
                'success': True,
                'message': 'LLM connection successful',
                'model': self.llm_analyzer.model_name,
                'response_received': bool(result)
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'LLM connection failed: {str(e)}',
                'model': self.llm_analyzer.model_name,
                'error': str(e)
            }
