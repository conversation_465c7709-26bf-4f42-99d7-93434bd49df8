import io
import base64
from typing import List, Dict, Any
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE


class DocxGenerator:
    """Generate structured DOCX documents from analyzed slide data."""

    def __init__(self):
        self.document = None

    def create_document(self, presentation_analysis: Dict[str, Any],
                       all_slides_data: List[Dict[str, Any]],
                       output_path: str) -> str:
        """Create a structured DOCX document from presentation analysis."""

        self.document = Document()
        self._setup_document_styles()

        # Add document title
        document_title = presentation_analysis.get('document_title', 'Presentation Content')
        title = self.document.add_heading(document_title, 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Collect all images for reference
        all_images = self._collect_all_images(all_slides_data)

        # Process each section
        sections = presentation_analysis.get('sections', [])
        for section in sections:
            self._add_section_content(section, all_images)

        # Save document
        self.document.save(output_path)
        return output_path

    def create_document_legacy(self, slides_analysis: List[Dict[str, Any]],
                              slides_data: List[Dict[str, Any]],
                              output_path: str) -> str:
        """Legacy method for slide-by-slide processing - kept for compatibility."""

        # Convert old format to new format for processing
        presentation_analysis = {
            'document_title': 'Presentation Content',
            'sections': [],
            'content_flow_notes': 'Legacy slide-by-slide processing'
        }

        # Convert each slide to a section
        for slide_analysis, slide_data in zip(slides_analysis, slides_data):
            section = {
                'section_title': slide_analysis.get('title', f"Slide {slide_analysis.get('slide_number', 1)}"),
                'heading_level': 1,
                'content_blocks': []
            }

            # Add subtitle if exists
            if slide_analysis.get('subtitle'):
                section['content_blocks'].append({
                    'type': 'paragraph',
                    'content': slide_analysis['subtitle'],
                    'style_hints': ['bold']
                })

            # Add body text
            for text_block in slide_analysis.get('body_text', []):
                section['content_blocks'].append(text_block)

            # Add images
            for img_context in slide_analysis.get('images', []):
                section['content_blocks'].append({
                    'type': 'image',
                    'image_index': img_context.get('image_index', 0),
                    'context': img_context.get('context', '')
                })

            presentation_analysis['sections'].append(section)

        return self.create_document(presentation_analysis, slides_data, output_path)

    def _setup_document_styles(self):
        """Setup custom styles for the document."""
        styles = self.document.styles

        # Create or modify heading styles
        try:
            slide_title_style = styles['Heading 1']
        except KeyError:
            slide_title_style = styles.add_style('Slide Title', WD_STYLE_TYPE.PARAGRAPH)

        slide_title_style.font.size = Pt(16)
        slide_title_style.font.bold = True

    def _collect_all_images(self, all_slides_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Collect all images from all slides into a single list."""
        all_images = []

        for slide_data in all_slides_data:
            slide_images = slide_data.get('images', [])
            all_images.extend(slide_images)

        return all_images

    def _add_section_content(self, section: Dict[str, Any], all_images: List[Dict[str, Any]]):
        """Add content from a thematic section to the document."""

        # Add section title
        section_title = section.get('section_title', 'Untitled Section')
        heading_level = section.get('heading_level', 1)

        # Ensure heading level is within Word's limits (1-9)
        heading_level = max(1, min(9, heading_level))

        title_para = self.document.add_heading(section_title, level=heading_level)
        title_para.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Process content blocks
        content_blocks = section.get('content_blocks', [])
        for block in content_blocks:
            self._add_content_block(block, all_images)

        # Add spacing after section
        self.document.add_paragraph()

    def _add_content_block(self, block: Dict[str, Any], all_images: List[Dict[str, Any]]):
        """Add a single content block to the document."""

        block_type = block.get('type', 'paragraph')

        if block_type == 'paragraph':
            self._add_paragraph_block(block)
        elif block_type == 'list':
            self._add_list_block(block)
        elif block_type == 'image':
            self._add_image_block(block, all_images)
        else:
            # Default to paragraph for unknown types
            self._add_paragraph_block(block)

    def _add_paragraph_block(self, block: Dict[str, Any]):
        """Add a paragraph block to the document."""
        content = block.get('content', '')
        style_hints = block.get('style_hints', [])

        para = self.document.add_paragraph()
        run = para.add_run(content)

        # Apply style hints
        if 'bold' in style_hints:
            run.bold = True
        if 'italic' in style_hints:
            run.italic = True

    def _add_list_block(self, block: Dict[str, Any]):
        """Add a list block to the document."""
        items = block.get('items', [])
        list_type = block.get('list_type', 'bullet')

        for item in items:
            if list_type == 'bullet':
                para = self.document.add_paragraph(item, style='List Bullet')
            else:
                para = self.document.add_paragraph(item, style='List Number')

    def _add_image_block(self, block: Dict[str, Any], all_images: List[Dict[str, Any]]):
        """Add an image block to the document."""
        image_index = block.get('image_index', 0)

        # Find the corresponding image data
        if image_index < len(all_images):
            image_data = all_images[image_index]

            try:
                # Decode base64 image data
                img_bytes = base64.b64decode(image_data['data'])
                img_stream = io.BytesIO(img_bytes)

                # Add image to document
                para = self.document.add_paragraph()
                para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # Add image with reasonable size (max 6 inches wide)
                run = para.runs[0] if para.runs else para.add_run()
                run.add_picture(img_stream, width=Inches(6))

                # Note: No image captions as per requirements

            except Exception as e:
                print(f"Error adding image {image_index}: {e}")
                # Add placeholder text instead
                para = self.document.add_paragraph()
                run = para.add_run(f"[Image {image_index + 1}: Unable to load image]")
                run.italic = True

    def _add_slide_content(self, slide_analysis: Dict[str, Any], slide_data: Dict[str, Any]):
        """Add content from a single slide to the document."""

        slide_num = slide_analysis.get('slide_number', 1)

        # Add slide separator
        separator = self.document.add_paragraph()
        separator.add_run(f"--- Slide {slide_num} ---").bold = True
        separator.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add title
        title = slide_analysis.get('title')
        if title:
            title_para = self.document.add_heading(title, level=1)
            title_para.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Add subtitle
        subtitle = slide_analysis.get('subtitle')
        if subtitle:
            subtitle_para = self.document.add_heading(subtitle, level=2)
            subtitle_para.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Add body text and images
        body_text = slide_analysis.get('body_text', [])
        images = slide_analysis.get('images', [])
        slide_images = slide_data.get('images', [])

        # Process body text
        for text_block in body_text:
            content = text_block.get('content', '')
            text_type = text_block.get('type', 'paragraph')
            style_hints = text_block.get('style_hints', [])

            if text_type == 'list' or text_type == 'bullet_point':
                # Handle as bullet point
                para = self.document.add_paragraph(content, style='List Bullet')
            else:
                # Handle as regular paragraph
                para = self.document.add_paragraph()
                run = para.add_run(content)

                # Apply style hints
                if 'bold' in style_hints:
                    run.bold = True
                if 'italic' in style_hints:
                    run.italic = True

        # Add images
        self._add_images_to_document(images, slide_images)

        # Add spacing after slide content
        self.document.add_paragraph()

    def _add_images_to_document(self, image_contexts: List[Dict[str, Any]],
                               slide_images: List[Dict[str, Any]]):
        """Add images to the document based on context."""

        for img_context in image_contexts:
            image_index = img_context.get('image_index', 0)

            # Find the corresponding image data
            if image_index < len(slide_images):
                image_data = slide_images[image_index]

                try:
                    # Decode base64 image data
                    img_bytes = base64.b64decode(image_data['data'])
                    img_stream = io.BytesIO(img_bytes)

                    # Add image to document
                    para = self.document.add_paragraph()
                    para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                    # Add image with reasonable size (max 6 inches wide)
                    run = para.runs[0] if para.runs else para.add_run()
                    run.add_picture(img_stream, width=Inches(6))

                    # Add image caption if context is available
                    context = img_context.get('context', '')
                    if context and context != f"Image {image_index+1} from slide":
                        caption_para = self.document.add_paragraph()
                        caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        caption_run = caption_para.add_run(f"Figure: {context}")
                        caption_run.italic = True
                        caption_run.font.size = Pt(10)

                except Exception as e:
                    print(f"Error adding image {image_index}: {e}")
                    # Add placeholder text instead
                    para = self.document.add_paragraph()
                    run = para.add_run(f"[Image {image_index + 1}: {img_context.get('context', 'Unable to load image')}]")
                    run.italic = True

    def get_document_stats(self) -> Dict[str, Any]:
        """Get statistics about the generated document."""
        if not self.document:
            return {}

        paragraph_count = len(self.document.paragraphs)

        # Count words (simple approximation)
        word_count = 0
        for para in self.document.paragraphs:
            word_count += len(para.text.split())

        return {
            'paragraphs': paragraph_count,
            'estimated_words': word_count,
            'pages_estimate': max(1, word_count // 250)  # Rough estimate
        }
