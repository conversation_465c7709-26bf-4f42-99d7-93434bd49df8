import io
import base64
from typing import List, Dict, Any
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE


class DocxGenerator:
    """Generate structured DOCX documents from analyzed slide data."""
    
    def __init__(self):
        self.document = None
    
    def create_document(self, slides_analysis: List[Dict[str, Any]], 
                       slides_data: List[Dict[str, Any]], 
                       output_path: str) -> str:
        """Create a structured DOCX document from slide analysis."""
        
        self.document = Document()
        self._setup_document_styles()
        
        # Add document title
        title = self.document.add_heading('Presentation Content', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Process each slide
        for slide_analysis, slide_data in zip(slides_analysis, slides_data):
            self._add_slide_content(slide_analysis, slide_data)
            
            # Add page break between slides (except for the last one)
            if slide_analysis != slides_analysis[-1]:
                self.document.add_page_break()
        
        # Save document
        self.document.save(output_path)
        return output_path
    
    def _setup_document_styles(self):
        """Setup custom styles for the document."""
        styles = self.document.styles
        
        # Create or modify heading styles
        try:
            slide_title_style = styles['Heading 1']
        except KeyError:
            slide_title_style = styles.add_style('Slide Title', WD_STYLE_TYPE.PARAGRAPH)
        
        slide_title_style.font.size = Pt(16)
        slide_title_style.font.bold = True
        
        try:
            slide_subtitle_style = styles['Heading 2']
        except KeyError:
            slide_subtitle_style = styles.add_style('Slide Subtitle', WD_STYLE_TYPE.PARAGRAPH)
        
        slide_subtitle_style.font.size = Pt(14)
        slide_subtitle_style.font.bold = True
    
    def _add_slide_content(self, slide_analysis: Dict[str, Any], slide_data: Dict[str, Any]):
        """Add content from a single slide to the document."""
        
        slide_num = slide_analysis.get('slide_number', 1)
        
        # Add slide separator
        separator = self.document.add_paragraph()
        separator.add_run(f"--- Slide {slide_num} ---").bold = True
        separator.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add title
        title = slide_analysis.get('title')
        if title:
            title_para = self.document.add_heading(title, level=1)
            title_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        
        # Add subtitle
        subtitle = slide_analysis.get('subtitle')
        if subtitle:
            subtitle_para = self.document.add_heading(subtitle, level=2)
            subtitle_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        
        # Add body text and images
        body_text = slide_analysis.get('body_text', [])
        images = slide_analysis.get('images', [])
        slide_images = slide_data.get('images', [])
        
        # Process body text
        for text_block in body_text:
            content = text_block.get('content', '')
            text_type = text_block.get('type', 'paragraph')
            style_hints = text_block.get('style_hints', [])
            
            if text_type == 'list' or text_type == 'bullet_point':
                # Handle as bullet point
                para = self.document.add_paragraph(content, style='List Bullet')
            else:
                # Handle as regular paragraph
                para = self.document.add_paragraph()
                run = para.add_run(content)
                
                # Apply style hints
                if 'bold' in style_hints:
                    run.bold = True
                if 'italic' in style_hints:
                    run.italic = True
        
        # Add images
        self._add_images_to_document(images, slide_images)
        
        # Add spacing after slide content
        self.document.add_paragraph()
    
    def _add_images_to_document(self, image_contexts: List[Dict[str, Any]], 
                               slide_images: List[Dict[str, Any]]):
        """Add images to the document based on context."""
        
        for img_context in image_contexts:
            image_index = img_context.get('image_index', 0)
            
            # Find the corresponding image data
            if image_index < len(slide_images):
                image_data = slide_images[image_index]
                
                try:
                    # Decode base64 image data
                    img_bytes = base64.b64decode(image_data['data'])
                    img_stream = io.BytesIO(img_bytes)
                    
                    # Add image to document
                    para = self.document.add_paragraph()
                    para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    
                    # Add image with reasonable size (max 6 inches wide)
                    run = para.runs[0] if para.runs else para.add_run()
                    run.add_picture(img_stream, width=Inches(6))
                    
                    # Add image caption if context is available
                    context = img_context.get('context', '')
                    if context and context != f"Image {image_index+1} from slide":
                        caption_para = self.document.add_paragraph()
                        caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        caption_run = caption_para.add_run(f"Figure: {context}")
                        caption_run.italic = True
                        caption_run.font.size = Pt(10)
                    
                except Exception as e:
                    print(f"Error adding image {image_index}: {e}")
                    # Add placeholder text instead
                    para = self.document.add_paragraph()
                    run = para.add_run(f"[Image {image_index + 1}: {img_context.get('context', 'Unable to load image')}]")
                    run.italic = True
    
    def get_document_stats(self) -> Dict[str, Any]:
        """Get statistics about the generated document."""
        if not self.document:
            return {}
        
        paragraph_count = len(self.document.paragraphs)
        
        # Count words (simple approximation)
        word_count = 0
        for para in self.document.paragraphs:
            word_count += len(para.text.split())
        
        return {
            'paragraphs': paragraph_count,
            'estimated_words': word_count,
            'pages_estimate': max(1, word_count // 250)  # Rough estimate
        }
