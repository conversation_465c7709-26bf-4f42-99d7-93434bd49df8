#!/usr/bin/env python3
"""
Debug script to find why output is empty.
"""

import os
import tempfile
from pptx import Presentation
from slide_processor import SlideProcessor
from content_extractor import ContentExtractor
from llm_analyzer import LLMAnalyzer
from docx_generator import Docx<PERSON>enerator


def create_simple_test_presentation():
    """Create a very simple test presentation."""
    
    prs = Presentation()
    
    # Slide 1: Simple slide
    slide1 = prs.slides.add_slide(prs.slide_layouts[1])
    title1 = slide1.shapes.title
    content1 = slide1.placeholders[1]
    title1.text = "Test Title"
    content1.text = "This is test content.\nSecond line of content."
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as tmp:
        prs.save(tmp.name)
        return tmp.name


def debug_step_by_step():
    """Debug each step of the process."""
    
    print("🔍 Debugging Empty Output Issue")
    print("=" * 50)
    
    # Create test file
    test_file = create_simple_test_presentation()
    print(f"✅ Created test file: {test_file}")
    
    try:
        # Step 1: Test content extraction
        print("\n📖 Step 1: Testing Content Extraction")
        extractor = ContentExtractor()
        slides_data = extractor.extract_from_pptx(test_file)
        
        print(f"   Slides extracted: {len(slides_data)}")
        for i, slide in enumerate(slides_data):
            print(f"   Slide {i+1}:")
            print(f"     Raw text: '{slide.get('raw_text', '')}'")
            print(f"     Images: {len(slide.get('images', []))}")
        
        if not slides_data or not slides_data[0].get('raw_text'):
            print("❌ Content extraction failed!")
            return False
        
        # Step 2: Test LLM analysis
        print("\n🤖 Step 2: Testing LLM Analysis")
        analyzer = LLMAnalyzer()
        presentation_analysis = analyzer.analyze_presentation(slides_data)
        
        print(f"   Document title: '{presentation_analysis.get('document_title', 'None')}'")
        print(f"   Sections: {len(presentation_analysis.get('sections', []))}")
        
        sections = presentation_analysis.get('sections', [])
        for i, section in enumerate(sections):
            print(f"   Section {i+1}:")
            print(f"     Title: '{section.get('section_title', 'None')}'")
            print(f"     Content blocks: {len(section.get('content_blocks', []))}")
            
            blocks = section.get('content_blocks', [])
            for j, block in enumerate(blocks):
                print(f"       Block {j+1}: {block.get('type', 'unknown')} - '{block.get('content', '')[:50]}...'")
        
        if not sections:
            print("❌ LLM analysis produced no sections!")
            return False
        
        # Step 3: Test document generation
        print("\n📝 Step 3: Testing Document Generation")
        generator = DocxGenerator()
        
        output_path = "debug_test_output.docx"
        result_path = generator.create_document(presentation_analysis, slides_data, output_path)
        
        print(f"   Output path: {result_path}")
        
        if os.path.exists(result_path):
            size = os.path.getsize(result_path)
            print(f"   File size: {size:,} bytes")
            
            if size < 1000:
                print("⚠️ File size is very small - might be empty!")
            else:
                print("✅ File generated successfully!")
            
            # Get document stats
            stats = generator.get_document_stats()
            print(f"   Document stats: {stats}")
            
            return size > 1000
        else:
            print("❌ Output file not created!")
            return False
    
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.unlink(test_file)


def test_document_generation_directly():
    """Test document generation with mock data."""
    
    print("\n🧪 Testing Document Generation with Mock Data")
    print("=" * 50)
    
    # Create mock presentation analysis
    mock_analysis = {
        "document_title": "Test Document",
        "sections": [
            {
                "section_title": "Introduction",
                "heading_level": 1,
                "content_blocks": [
                    {
                        "type": "paragraph",
                        "content": "This is the introduction paragraph.",
                        "style_hints": []
                    },
                    {
                        "type": "paragraph",
                        "content": "This is another paragraph with some content.",
                        "style_hints": ["bold"]
                    }
                ]
            },
            {
                "section_title": "Main Content",
                "heading_level": 1,
                "content_blocks": [
                    {
                        "type": "list",
                        "items": ["First item", "Second item", "Third item"],
                        "list_type": "bullet"
                    }
                ]
            }
        ],
        "content_flow_notes": "Mock data for testing"
    }
    
    # Mock slides data
    mock_slides_data = [
        {"slide_number": 1, "images": []},
        {"slide_number": 2, "images": []}
    ]
    
    try:
        generator = DocxGenerator()
        output_path = "mock_test_output.docx"
        
        result_path = generator.create_document(mock_analysis, mock_slides_data, output_path)
        
        if os.path.exists(result_path):
            size = os.path.getsize(result_path)
            print(f"✅ Mock document created: {size:,} bytes")
            
            stats = generator.get_document_stats()
            print(f"📊 Stats: {stats}")
            
            return True
        else:
            print("❌ Mock document not created!")
            return False
    
    except Exception as e:
        print(f"❌ Error creating mock document: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all debug tests."""
    
    print("🚀 Debugging Empty Output Issue")
    print("=" * 60)
    
    # Test 1: Step by step debugging
    step_success = debug_step_by_step()
    
    # Test 2: Mock data test
    mock_success = test_document_generation_directly()
    
    # Summary
    print("\n" + "=" * 60)
    print("🔍 DEBUG SUMMARY")
    print("=" * 60)
    
    print(f"📋 Step-by-step test: {'✅ PASS' if step_success else '❌ FAIL'}")
    print(f"🧪 Mock data test: {'✅ PASS' if mock_success else '❌ FAIL'}")
    
    if step_success and mock_success:
        print("\n✅ Document generation is working correctly!")
        print("The empty output issue might be in the web interface or file handling.")
    elif mock_success and not step_success:
        print("\n⚠️ Document generation works with mock data but fails with real data.")
        print("Issue is likely in content extraction or LLM analysis.")
    elif not mock_success:
        print("\n❌ Document generation has fundamental issues.")
        print("Check the DocxGenerator implementation.")
    
    print("\n🔧 Next steps:")
    print("1. Check the generated files manually")
    print("2. Verify web interface file handling")
    print("3. Test with different input files")


if __name__ == "__main__":
    main()
