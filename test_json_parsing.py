#!/usr/bin/env python3
"""
Test the improved JSON parsing logic.
"""

import json

def test_parse_response(response: str, slide_number: int = 1):
    """Test the JSON parsing logic."""
    print(f"Testing response: {response[:100]}...")
    
    try:
        # Clean up the response
        response = response.strip()
        
        # Handle various markdown code block formats
        if '```json' in response:
            # Extract content between ```json and ```
            start = response.find('```json') + 7
            end = response.find('```', start)
            if end != -1:
                response = response[start:end].strip()
        elif '```' in response:
            # Extract content between ``` and ```
            start = response.find('```') + 3
            end = response.find('```', start)
            if end != -1:
                response = response[start:end].strip()
        
        # Remove any leading/trailing whitespace and newlines
        response = response.strip('\n\r\t ')
        
        # Try to find JSON object in the response
        if not response.startswith('{'):
            # Look for the first { and last }
            start_idx = response.find('{')
            end_idx = response.rfind('}')
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                response = response[start_idx:end_idx + 1]
        
        parsed = json.loads(response)
        
        # Ensure slide_number is set correctly
        parsed['slide_number'] = slide_number
        
        print("✅ Parsing successful!")
        print(f"Title: {parsed.get('title')}")
        return parsed
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse: {e}")
        print(f"Cleaned response: {response}")
        return None

# Test cases
test_cases = [
    # Case 1: Response with markdown
    '''```json
{
    "slide_number": 1,
    "title": "Welcome to Our Company",
    "subtitle": "Building the Future Together",
    "body_text": [
        {
            "type": "paragraph",
            "content": "Innovation at our core",
            "style_hints": []
        }
    ],
    "images": [],
    "layout_notes": "Simple slide layout"
}
```''',
    
    # Case 2: Response with extra text
    '''Here is the JSON response:

{
    "slide_number": 1,
    "title": "Test Title",
    "subtitle": null,
    "body_text": [],
    "images": [],
    "layout_notes": "Test"
}

This completes the analysis.''',
    
    # Case 3: Clean JSON
    '''{
    "slide_number": 1,
    "title": "Clean JSON",
    "subtitle": null,
    "body_text": [],
    "images": [],
    "layout_notes": "Direct JSON"
}'''
]

print("🧪 Testing JSON Parsing Logic")
print("=" * 50)

for i, test_case in enumerate(test_cases, 1):
    print(f"\nTest Case {i}:")
    print("-" * 30)
    result = test_parse_response(test_case)
    if result:
        print(f"Result: {json.dumps(result, indent=2)}")
    print()
