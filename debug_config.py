#!/usr/bin/env python3
"""
Debug script to check configuration loading.
"""

import os
from dotenv import load_dotenv

print("🔍 Configuration Debug")
print("=" * 40)

# Load environment variables
load_dotenv()

# Check if .env file exists
env_file_exists = os.path.exists('.env')
print(f"📁 .env file exists: {env_file_exists}")

if env_file_exists:
    with open('.env', 'r') as f:
        content = f.read()
        print(f"📄 .env file content length: {len(content)} characters")
        print(f"📄 .env file lines: {len(content.splitlines())} lines")

# Check environment variables
api_key = os.getenv("OPENROUTER_API_KEY")
model_name = os.getenv("MODEL_NAME")

print(f"\n🔑 API Key loaded: {'Yes' if api_key else 'No'}")
if api_key:
    print(f"🔑 API Key preview: {api_key[:15]}...")
    print(f"🔑 API Key length: {len(api_key)} characters")

print(f"🤖 Model name: {model_name}")

# Test import of config module
try:
    import config
    print(f"\n📦 Config module loaded successfully")
    print(f"🔑 Config API Key: {'Yes' if config.OPENROUTER_API_KEY else 'No'}")
    if config.OPENROUTER_API_KEY:
        print(f"🔑 Config API Key preview: {config.OPENROUTER_API_KEY[:15]}...")
    print(f"🤖 Config Model: {config.MODEL_NAME}")
except Exception as e:
    print(f"\n❌ Error loading config module: {e}")

# Test LLM connection
try:
    from llm_analyzer import LLMAnalyzer
    analyzer = LLMAnalyzer()
    print(f"\n🧪 LLMAnalyzer created successfully")
    print(f"🔑 Analyzer API Key: {'Yes' if analyzer.api_key else 'No'}")
    print(f"🤖 Analyzer Model: {analyzer.model_name}")
except Exception as e:
    print(f"\n❌ Error creating LLMAnalyzer: {e}")
