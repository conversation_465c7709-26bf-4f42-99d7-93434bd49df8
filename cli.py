#!/usr/bin/env python3
"""
Command-line interface for the Slides to Structured A4 PDF Converter.

Usage:
    python cli.py input.pptx [output.docx]
    python cli.py input.pdf [output.docx]
"""

import sys
import os
import argparse
from slide_processor import SlideProcessor
import config


def main():
    """Main CLI function."""
    
    parser = argparse.ArgumentParser(
        description="Convert presentation slides to structured Word documents using AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python cli.py presentation.pptx
    python cli.py slides.pdf output_document.docx
    python cli.py --check-status
        """
    )
    
    parser.add_argument(
        'input_file',
        nargs='?',
        help='Input PPTX or PDF file'
    )
    
    parser.add_argument(
        'output_file',
        nargs='?',
        help='Output DOCX file (optional, auto-generated if not provided)'
    )
    
    parser.add_argument(
        '--check-status',
        action='store_true',
        help='Check system status and configuration'
    )
    
    parser.add_argument(
        '--api-key',
        help='OpenRouter API key (overrides environment variable)'
    )
    
    parser.add_argument(
        '--model',
        default=config.MODEL_NAME,
        help=f'LLM model to use (default: {config.MODEL_NAME})'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    args = parser.parse_args()
    
    # Handle API key override
    if args.api_key:
        os.environ["OPENROUTER_API_KEY"] = args.api_key
        config.OPENROUTER_API_KEY = args.api_key
    
    # Handle model override
    if args.model:
        config.MODEL_NAME = args.model
    
    # Check status command
    if args.check_status:
        check_system_status(args.verbose)
        return
    
    # Validate input file argument
    if not args.input_file:
        parser.error("Input file is required (use --check-status to check system status)")
    
    # Process file
    try:
        process_file(args.input_file, args.output_file, args.verbose)
    except Exception as e:
        print(f"❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def process_file(input_file: str, output_file: str = None, verbose: bool = False):
    """Process a presentation file."""
    
    # Validate input file
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"Input file not found: {input_file}")
    
    file_ext = os.path.splitext(input_file)[1].lower()
    if file_ext not in config.SUPPORTED_EXTENSIONS:
        raise ValueError(f"Unsupported file type: {file_ext}. Supported: {', '.join(config.SUPPORTED_EXTENSIONS)}")
    
    # Generate output filename if not provided
    if output_file is None:
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = f"{base_name}{config.DEFAULT_OUTPUT_SUFFIX}.docx"
    
    print(f"📄 Processing: {input_file}")
    print(f"📝 Output will be saved to: {output_file}")
    
    # Check API key
    if not config.OPENROUTER_API_KEY:
        raise ValueError("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable or use --api-key")
    
    # Initialize processor
    processor = SlideProcessor()
    
    if verbose:
        print(f"🤖 Using model: {config.MODEL_NAME}")
        print(f"🔧 Max tokens: {config.MAX_TOKENS}")
        print(f"🌡️ Temperature: {config.TEMPERATURE}")
    
    # Process the file
    try:
        output_path, stats = processor.process_file(input_file, output_file)
        
        # Display results
        print("\n✅ Processing completed successfully!")
        print(f"📁 Output saved to: {output_path}")
        
        # Display statistics
        print("\n📊 Processing Statistics:")
        print(f"   • Slides processed: {stats.get('total_slides', 0)}")
        print(f"   • Images found: {stats.get('total_images', 0)}")
        print(f"   • Text blocks: {stats.get('total_text_blocks', 0)}")
        print(f"   • Titles found: {stats.get('titles_found', 0)}")
        print(f"   • Subtitles found: {stats.get('subtitles_found', 0)}")
        
        if 'estimated_words' in stats:
            print(f"   • Estimated words: {stats['estimated_words']}")
        if 'pages_estimate' in stats:
            print(f"   • Estimated pages: {stats['pages_estimate']}")
        
        print(f"\n🎉 Your structured document is ready!")
        
    except Exception as e:
        raise Exception(f"Processing failed: {e}")


def check_system_status(verbose: bool = False):
    """Check and display system status."""
    
    print("🔍 Checking system status...\n")
    
    try:
        processor = SlideProcessor()
        validation = processor.validate_environment()
        
        print("System Components:")
        for component, status in validation.items():
            status_icon = "✅" if status else "❌"
            component_name = component.replace('_', ' ').title()
            print(f"   {status_icon} {component_name}")
            
            if verbose and not status:
                if component == 'api_key_configured':
                    print("      → Set OPENROUTER_API_KEY environment variable")
                elif component == 'llm_connection':
                    print("      → Check API key and internet connection")
        
        print()
        if all(validation.values()):
            print("🎉 All systems operational!")
        else:
            print("⚠️ Some components need attention.")
            print("   Use --verbose for more details.")
        
        if verbose:
            print(f"\nConfiguration:")
            print(f"   • Model: {config.MODEL_NAME}")
            print(f"   • Max tokens: {config.MAX_TOKENS}")
            print(f"   • Temperature: {config.TEMPERATURE}")
            print(f"   • Supported formats: {', '.join(config.SUPPORTED_EXTENSIONS)}")
            
    except Exception as e:
        print(f"❌ Error checking system status: {e}")
        if verbose:
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
