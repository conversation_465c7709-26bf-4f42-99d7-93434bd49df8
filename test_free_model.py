#!/usr/bin/env python3
"""
Test script to verify free models work with OpenRouter.
"""

import os
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_free_model(api_key, model_name="google/gemma-3-27b-it:free"):
    """Test a free model on OpenRouter."""
    
    if not api_key:
        print("❌ No API key provided")
        return False
    
    print(f"🧪 Testing model: {model_name}")
    print(f"🔑 API key: {api_key[:10]}...")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://github.com/your-repo",
        "X-Title": "Slides to A4 Converter - Free Model Test"
    }
    
    # Simple test prompt
    payload = {
        "model": model_name,
        "messages": [
            {
                "role": "user",
                "content": """Analyze this slide content and provide a structured JSON response:

Slide Content:
Welcome to Our Company
Building the Future Together

• Innovation at our core
• Customer-focused solutions  
• Global reach, local impact

Please respond with JSON format:
{
    "title": "main title",
    "subtitle": "subtitle if any", 
    "body_text": ["list of text items"]
}"""
            }
        ],
        "max_tokens": 500,
        "temperature": 0.1
    }
    
    try:
        print("📡 Making API call...")
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            print("✅ API call successful!")
            print(f"📝 Response length: {len(content)} characters")
            print(f"💰 Usage: {result.get('usage', 'Not provided')}")
            print("\n📄 Response content:")
            print("-" * 50)
            print(content)
            print("-" * 50)
            
            # Try to parse as JSON
            try:
                parsed = json.loads(content.strip())
                print("✅ Response is valid JSON!")
                print(f"🎯 Extracted title: {parsed.get('title', 'None')}")
                return True
            except json.JSONDecodeError:
                print("⚠️ Response is not JSON, but API call worked")
                return True
                
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"📄 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    
    print("🆓 Free Model Testing for OpenRouter")
    print("=" * 50)
    
    # Get API key
    api_key = os.getenv("OPENROUTER_API_KEY")
    
    if not api_key:
        print("⚠️ No API key found in environment variables")
        api_key = input("Enter your OpenRouter API key: ").strip()
    
    if not api_key:
        print("❌ No API key provided. Exiting.")
        return
    
    # Test free models
    free_models = [
        "google/gemma-3-27b-it:free",
        "meta-llama/llama-3.2-3b-instruct:free",
        "microsoft/phi-3-mini-128k-instruct:free",
        "google/gemma-2-9b-it:free"
    ]
    
    print(f"\n🧪 Testing {len(free_models)} free models...")
    
    results = {}
    
    for model in free_models:
        print(f"\n{'='*60}")
        print(f"Testing: {model}")
        print('='*60)
        
        success = test_free_model(api_key, model)
        results[model] = success
        
        if success:
            print(f"✅ {model} - SUCCESS")
        else:
            print(f"❌ {model} - FAILED")
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print('='*60)
    
    successful = sum(results.values())
    total = len(results)
    
    print(f"📊 Results: {successful}/{total} models working")
    
    for model, success in results.items():
        status = "✅" if success else "❌"
        print(f"{status} {model}")
    
    if successful > 0:
        print(f"\n🎉 Great! You have {successful} working free model(s)")
        print("💡 You can now use the converter without any costs!")
        
        # Update config recommendation
        working_models = [model for model, success in results.items() if success]
        best_model = working_models[0]
        
        print(f"\n🔧 Recommended configuration:")
        print(f"MODEL_NAME={best_model}")
        
    else:
        print("\n❌ No free models are working")
        print("🔍 Please check your API key and try again")

if __name__ == "__main__":
    main()
